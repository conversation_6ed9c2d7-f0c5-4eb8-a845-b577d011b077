//
//  ContentView.swift
//  StealthSearch
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var viewModel = SearchViewModel()

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 应用标题和图标
                VStack(spacing: 10) {
                    Image(systemName: "magnifyingglass.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)

                    Text("隐蔽搜索")
                        .font(.title)
                        .fontWeight(.bold)

                    Text("快速、便捷、隐蔽的搜索助手")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 20)

                // 搜索输入区域
                VStack(spacing: 15) {
                    HStack {
                        TextField("输入搜索关键词...", text: $viewModel.searchText)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .onSubmit {
                                viewModel.performSearch()
                            }

                        Button(action: {
                            viewModel.performSearch()
                        }) {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(.white)
                                .frame(width: 40, height: 40)
                                .background(viewModel.isSearchValid ? Color.blue : Color.gray)
                                .cornerRadius(8)
                        }
                        .disabled(!viewModel.isSearchValid)
                    }
                    .padding(.horizontal)
                }

                // 搜索历史
                if !viewModel.searchHistory.isEmpty {
                    VStack(alignment: .leading, spacing: 10) {
                        HStack {
                            Text("搜索历史")
                                .font(.headline)
                                .foregroundColor(.primary)

                            Spacer()

                            Button("清除") {
                                viewModel.clearAllHistory()
                            }
                            .font(.caption)
                            .foregroundColor(.red)
                        }
                        .padding(.horizontal)

                        ScrollView {
                            LazyVStack(spacing: 8) {
                                ForEach(viewModel.searchHistory, id: \.self) { query in
                                    SearchHistoryRow(
                                        query: query,
                                        onTap: { viewModel.searchWithHistory(query) },
                                        onDelete: { viewModel.removeHistoryItem(query) }
                                    )
                                }
                            }
                            .padding(.horizontal)
                        }
                    }
                } else {
                    VStack(spacing: 10) {
                        Image(systemName: "clock.arrow.circlepath")
                            .font(.system(size: 40))
                            .foregroundColor(.gray)

                        Text("暂无搜索历史")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top, 40)
                }

                Spacer()
            }
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        viewModel.showingSettings = true
                    }) {
                        Image(systemName: "gearshape.fill")
                    }
                }
            }
            .sheet(isPresented: $viewModel.showingSettings) {
                SettingsView()
            }
        }
    }
}

struct SearchHistoryRow: View {
    let query: String
    let onTap: () -> Void
    let onDelete: () -> Void

    var body: some View {
        HStack {
            Button(action: onTap) {
                HStack {
                    Image(systemName: "clock")
                        .foregroundColor(.gray)
                        .frame(width: 20)

                    Text(query)
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    Spacer()

                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.blue)
                        .font(.caption)
                }
                .padding(.vertical, 8)
                .padding(.horizontal, 12)
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .buttonStyle(PlainButtonStyle())

            Button(action: onDelete) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.gray)
            }
        }
    }
}

#Preview {
    ContentView()
}
