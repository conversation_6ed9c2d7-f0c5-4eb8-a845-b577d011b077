//
//  APPApp.swift
//  StealthSearch
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI

@main
struct StealthSearchApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
                .onOpenURL { url in
                    handleURL(url)
                }
        }
    }

    /// 处理URL Scheme跳转
    /// - Parameter url: 传入的URL
    private func handleURL(_ url: URL) {
        guard url.scheme == "stealthsearch" else { return }

        switch url.host {
        case "search":
            // 处理搜索跳转
            if let query = url.queryParameters["q"] {
                SearchEngine.shared.quickSearch(query: query)
            }
        default:
            break
        }
    }
}

// URL参数解析扩展
extension URL {
    var queryParameters: [String: String] {
        guard let components = URLComponents(url: self, resolvingAgainstBaseURL: true),
              let queryItems = components.queryItems else {
            return [:]
        }

        var parameters: [String: String] = [:]
        for item in queryItems {
            parameters[item.name] = item.value
        }
        return parameters
    }
}
