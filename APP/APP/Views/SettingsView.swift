//
//  SettingsView.swift
//  StealthSearch
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = SettingsViewModel()
    
    var body: some View {
        NavigationView {
            Form {
                // 搜索设置
                Section("搜索设置") {
                    Picker("搜索引擎", selection: $viewModel.selectedProvider) {
                        ForEach(SearchProvider.allCases, id: \.self) { provider in
                            HStack {
                                Image(systemName: provider.iconName)
                                Text(provider.displayName)
                            }
                            .tag(provider)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                // 功能设置
                Section("功能设置") {
                    Toggle("启用Siri快捷指令", isOn: $viewModel.enableSiriShortcuts)
                        .onChange(of: viewModel.enableSiriShortcuts) { value in
                            viewModel.updateSiriShortcuts(value)
                        }
                    
                    Toggle("启用Widget小组件", isOn: $viewModel.enableWidget)
                        .onChange(of: viewModel.enableWidget) { value in
                            viewModel.updateWidget(value)
                        }
                }
                
                // 隐私设置
                Section("隐私设置") {
                    Toggle("保存搜索历史", isOn: $viewModel.saveSearchHistory)
                        .onChange(of: viewModel.saveSearchHistory) { value in
                            viewModel.updateSaveSearchHistory(value)
                        }
                    
                    Button("清除搜索历史") {
                        viewModel.showingClearHistoryAlert = true
                    }
                    .foregroundColor(.red)
                }
                
                // 关于信息
                Section("关于") {
                    HStack {
                        Text("应用版本")
                        Spacer()
                        Text("1.0.0")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("开发者")
                        Spacer()
                        Text("StealthSearch Team")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            .alert("清除搜索历史", isPresented: $viewModel.showingClearHistoryAlert) {
                Button("取消", role: .cancel) { }
                Button("清除", role: .destructive) {
                    viewModel.clearSearchHistory()
                }
            } message: {
                Text("确定要清除所有搜索历史吗？此操作无法撤销。")
            }
        }
    }
}

class SettingsViewModel: ObservableObject {
    @Published var selectedProvider: SearchProvider
    @Published var enableSiriShortcuts: Bool
    @Published var enableWidget: Bool
    @Published var saveSearchHistory: Bool
    @Published var showingClearHistoryAlert = false
    
    private let dataManager = DataManager.shared
    
    init() {
        self.selectedProvider = dataManager.getSearchProvider()
        self.enableSiriShortcuts = dataManager.getSiriShortcutsEnabled()
        self.enableWidget = dataManager.getWidgetEnabled()
        self.saveSearchHistory = dataManager.getSaveSearchHistoryEnabled()
        
        // 监听搜索引擎变化
        $selectedProvider
            .dropFirst()
            .sink { [weak self] provider in
                self?.dataManager.saveSearchProvider(provider)
            }
            .store(in: &cancellables)
    }
    
    private var cancellables = Set<AnyCancellable>()
    
    func updateSiriShortcuts(_ enabled: Bool) {
        dataManager.setSiriShortcutsEnabled(enabled)
    }
    
    func updateWidget(_ enabled: Bool) {
        dataManager.setWidgetEnabled(enabled)
    }
    
    func updateSaveSearchHistory(_ enabled: Bool) {
        dataManager.setSaveSearchHistoryEnabled(enabled)
    }
    
    func clearSearchHistory() {
        dataManager.clearSearchHistory()
    }
}

import Combine

#Preview {
    SettingsView()
}
