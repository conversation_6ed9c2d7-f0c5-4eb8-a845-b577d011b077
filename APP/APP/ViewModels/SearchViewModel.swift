//
//  SearchViewModel.swift
//  StealthSearch
//
//  Created by tongsy on 2025/8/2.
//

import Foundation
import Combine

class SearchViewModel: ObservableObject {
    @Published var searchText = ""
    @Published var searchHistory: [String] = []
    @Published var isSearching = false
    @Published var showingSettings = false
    
    private let searchEngine = SearchEngine.shared
    private let dataManager = DataManager.shared
    
    init() {
        loadSearchHistory()
    }
    
    /// 执行搜索
    func performSearch() {
        let query = searchText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !query.isEmpty else { return }
        
        isSearching = true
        
        // 执行搜索
        searchEngine.quickSearch(query: query)
        
        // 清空搜索框
        searchText = ""
        
        // 更新搜索历史
        loadSearchHistory()
        
        isSearching = false
    }
    
    /// 使用历史记录搜索
    /// - Parameter query: 搜索关键词
    func searchWithHistory(_ query: String) {
        searchText = query
        performSearch()
    }
    
    /// 删除搜索历史项
    /// - Parameter query: 要删除的搜索关键词
    func removeHistoryItem(_ query: String) {
        dataManager.removeSearchHistory(query)
        loadSearchHistory()
    }
    
    /// 清除所有搜索历史
    func clearAllHistory() {
        dataManager.clearSearchHistory()
        loadSearchHistory()
    }
    
    /// 加载搜索历史
    private func loadSearchHistory() {
        searchHistory = dataManager.getSearchHistory()
    }
    
    /// 验证搜索输入
    var isSearchValid: Bool {
        searchEngine.isValidQuery(searchText)
    }
}
