//
//  SearchEngine.swift
//  StealthSearch
//
//  Created by tongsy on 2025/8/2.
//

import Foundation
import UIKit

class SearchEngine {
    static let shared = SearchEngine()
    
    private init() {}
    
    /// 执行搜索操作
    /// - Parameters:
    ///   - query: 搜索关键词
    ///   - provider: 搜索引擎提供商
    func search(query: String, provider: SearchProvider) {
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }
        
        // 保存搜索历史
        DataManager.shared.saveSearchHistory(query)
        
        // 编码搜索关键词
        let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let urlString = provider.rawValue + encodedQuery
        
        // 打开搜索URL
        if let url = URL(string: urlString) {
            DispatchQueue.main.async {
                UIApplication.shared.open(url, options: [:]) { success in
                    if !success {
                        print("Failed to open search URL: \(urlString)")
                    }
                }
            }
        }
    }
    
    /// 快速搜索（使用默认搜索引擎）
    /// - Parameter query: 搜索关键词
    func quickSearch(query: String) {
        let provider = DataManager.shared.getSearchProvider()
        search(query: query, provider: provider)
    }
    
    /// 验证搜索关键词
    /// - Parameter query: 搜索关键词
    /// - Returns: 是否有效
    func isValidQuery(_ query: String) -> Bool {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        return !trimmedQuery.isEmpty && trimmedQuery.count <= 500
    }
    
    /// 清理搜索关键词
    /// - Parameter query: 原始搜索关键词
    /// - Returns: 清理后的搜索关键词
    func cleanQuery(_ query: String) -> String {
        return query.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}
