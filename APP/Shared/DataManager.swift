//
//  DataManager.swift
//  StealthSearch
//
//  Created by tongsy on 2025/8/2.
//

import Foundation

class DataManager {
    static let shared = DataManager()
    
    private let userDefaults: UserDefaults
    private let maxHistoryCount = 20
    
    // App Group标识符
    private let appGroupIdentifier = "group.com.example.stealthsearch"
    
    // 存储键
    private struct Keys {
        static let searchHistory = "searchHistory"
        static let searchProvider = "searchProvider"
        static let enableSiriShortcuts = "enableSiriShortcuts"
        static let enableWidget = "enableWidget"
        static let saveSearchHistory = "saveSearchHistory"
    }
    
    private init() {
        // 使用App Group共享数据
        if let groupDefaults = UserDefaults(suiteName: appGroupIdentifier) {
            self.userDefaults = groupDefaults
        } else {
            // 如果App Group不可用，使用标准UserDefaults
            self.userDefaults = UserDefaults.standard
            print("Warning: App Group not available, using standard UserDefaults")
        }
    }
    
    // MARK: - 搜索历史管理
    
    /// 保存搜索历史
    /// - Parameter query: 搜索关键词
    func saveSearchHistory(_ query: String) {
        guard getSaveSearchHistoryEnabled() else { return }
        
        let cleanQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !cleanQuery.isEmpty else { return }
        
        var history = getSearchHistory()
        
        // 移除重复项
        history.removeAll { $0 == cleanQuery }
        
        // 添加到开头
        history.insert(cleanQuery, at: 0)
        
        // 限制历史记录数量
        history = Array(history.prefix(maxHistoryCount))
        
        userDefaults.set(history, forKey: Keys.searchHistory)
    }
    
    /// 获取搜索历史
    /// - Returns: 搜索历史数组
    func getSearchHistory() -> [String] {
        return userDefaults.stringArray(forKey: Keys.searchHistory) ?? []
    }
    
    /// 清除搜索历史
    func clearSearchHistory() {
        userDefaults.removeObject(forKey: Keys.searchHistory)
    }
    
    /// 删除特定搜索历史项
    /// - Parameter query: 要删除的搜索关键词
    func removeSearchHistory(_ query: String) {
        var history = getSearchHistory()
        history.removeAll { $0 == query }
        userDefaults.set(history, forKey: Keys.searchHistory)
    }
    
    // MARK: - 搜索引擎配置
    
    /// 保存搜索引擎提供商
    /// - Parameter provider: 搜索引擎提供商
    func saveSearchProvider(_ provider: SearchProvider) {
        userDefaults.set(provider.rawValue, forKey: Keys.searchProvider)
    }
    
    /// 获取搜索引擎提供商
    /// - Returns: 当前设置的搜索引擎提供商
    func getSearchProvider() -> SearchProvider {
        let providerString = userDefaults.string(forKey: Keys.searchProvider) ?? SearchProvider.google.rawValue
        return SearchProvider(rawValue: providerString) ?? .google
    }
    
    // MARK: - 功能开关配置
    
    /// 设置Siri快捷指令开关
    /// - Parameter enabled: 是否启用
    func setSiriShortcutsEnabled(_ enabled: Bool) {
        userDefaults.set(enabled, forKey: Keys.enableSiriShortcuts)
    }
    
    /// 获取Siri快捷指令开关状态
    /// - Returns: 是否启用
    func getSiriShortcutsEnabled() -> Bool {
        return userDefaults.bool(forKey: Keys.enableSiriShortcuts)
    }
    
    /// 设置Widget开关
    /// - Parameter enabled: 是否启用
    func setWidgetEnabled(_ enabled: Bool) {
        userDefaults.set(enabled, forKey: Keys.enableWidget)
    }
    
    /// 获取Widget开关状态
    /// - Returns: 是否启用
    func getWidgetEnabled() -> Bool {
        return userDefaults.bool(forKey: Keys.enableWidget)
    }
    
    /// 设置保存搜索历史开关
    /// - Parameter enabled: 是否启用
    func setSaveSearchHistoryEnabled(_ enabled: Bool) {
        userDefaults.set(enabled, forKey: Keys.saveSearchHistory)
    }
    
    /// 获取保存搜索历史开关状态
    /// - Returns: 是否启用
    func getSaveSearchHistoryEnabled() -> Bool {
        // 默认启用
        return userDefaults.object(forKey: Keys.saveSearchHistory) as? Bool ?? true
    }
}
