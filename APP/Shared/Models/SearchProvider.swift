//
//  SearchProvider.swift
//  StealthSearch
//
//  Created by tongsy on 2025/8/2.
//

import Foundation

enum SearchProvider: String, CaseIterable {
    case google = "https://www.google.com/search?q="
    case bing = "https://www.bing.com/search?q="
    case duckduckgo = "https://duckduckgo.com/?q="
    case baidu = "https://www.baidu.com/s?wd="
    
    var displayName: String {
        switch self {
        case .google:
            return "Google"
        case .bing:
            return "Bing"
        case .duckduckgo:
            return "DuckDuckGo"
        case .baidu:
            return "百度"
        }
    }
    
    var iconName: String {
        switch self {
        case .google:
            return "globe"
        case .bing:
            return "globe.americas"
        case .duckduckgo:
            return "shield"
        case .baidu:
            return "globe.asia.australia"
        }
    }
}
